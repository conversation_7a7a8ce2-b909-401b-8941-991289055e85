# Google Play API - Production Ready

A robust Google Play API service that automatically tries different markets and devices to find and download apps that may not be available in specific regions.

## Features

- **Multi-Market Support**: Automatically tries 20+ different markets (US, UK, Germany, France, Japan, etc.)
- **Multi-Device Support**: Tests with 40+ different device configurations
- **Smart Retry Logic**: Prioritizes likely-to-work combinations first
- **Caching**: Remembers successful market/device combinations for faster subsequent requests
- **Production Ready**: Proper error handling, logging, health checks
- **Docker Support**: Runs in Debian 11 environment for SSL compatibility

## Quick Start

### 1. Setup Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your Google credentials
nano .env
```

### 2. Run with Docker Compose

```bash
# Build and start the service
docker-compose up -d

# Check logs
docker-compose logs -f

# Check health
curl http://localhost:5000/health
```

### 3. Test the API

```bash
# Search for an app
curl "http://localhost:5000/search/com.firsty.app"

# Search with details
curl "http://localhost:5000/search/com.firsty.app?details=1"

# Download an app (redirects to Google's download URL)
curl -I "http://localhost:5000/download/com.firsty.app"
```

## API Endpoints

### GET /health
Health check endpoint
```json
{
  "status": "healthy",
  "service": "google-play-api"
}
```

### GET /search/{app_id}
Search for apps across multiple markets/devices

**Parameters:**
- `details` (optional): Include full app details

**Response:**
```json
{
  "success": true,
  "app_id": "com.example.app",
  "apps": {
    "com.example.app": "1,000,000+"
  },
  "metadata": {
    "market": "US",
    "device": "hero2lte",
    "attempts": 2
  }
}
```

### GET /download/{app_id}
Get download URL for an app (redirects to Google's CDN)

**Response:** HTTP 302 redirect to download URL or JSON error

## Configuration

### Markets Tried (in order of priority)
1. Major markets: US, UK, Germany, France
2. All other markets: Spain, Italy, Japan, Korea, China, Brazil, Russia, Australia, Canada, India, Netherlands, Sweden, Norway, Denmark, Finland, Poland

### Devices Tried (priority devices first)
- hero2lte, angler, bullhead, hammerhead, sailfish, walleye, bacon, oneplus3, mako, shamu
- Plus 30+ additional device configurations

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `GOOGLE_EMAIL` | - | **Required**: Your Google account email |
| `GOOGLE_PASSWORD` | - | **Required**: Your Google account password |
| `FLASK_HOST` | 0.0.0.0 | Flask server host |
| `FLASK_PORT` | 5000 | Flask server port |
| `FLASK_DEBUG` | false | Enable Flask debug mode |
| `LOG_LEVEL` | INFO | Logging level (DEBUG, INFO, WARNING, ERROR) |

## Development

### Run without Docker

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export email="<EMAIL>"
export password="your-password"

# Run the API
python api.py
```

### Logs

The service provides detailed logging:
- INFO: Successful operations, market/device combinations used
- DEBUG: All attempts, detailed error messages
- ERROR: Failed requests after all retries

## Troubleshooting

### Common Issues

1. **SSL Errors**: The service requires Debian 11 for SSL compatibility with Google's servers
2. **Authentication**: Ensure your Google credentials are correct and 2FA is disabled for the account
3. **Rate Limiting**: Google may rate limit requests; the service includes retry logic with delays

### Monitoring

```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs google-play-api

# Check health endpoint
curl http://localhost:5000/health
```

## Security Notes

- Store credentials in `.env` file (never commit to git)
- The service runs as non-root user in Docker
- Consider using application-specific passwords for Google accounts
- Monitor logs for suspicious activity

## License

This project is for educational and research purposes only. Respect Google's Terms of Service.
