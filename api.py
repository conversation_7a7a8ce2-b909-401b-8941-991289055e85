import re
import logging
from flask import Flask, request, redirect, jsonify
from gplay_service import GooglePlayService
from config import FLASK_HOST, FLASK_PORT, FLASK_DEBUG, LOG_LEVEL

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
gplay_service = GooglePlayService()


@app.route('/', methods=["GET"])
def root():
    return redirect('https://blog.drehsec.tk/my-google-play-api/')


@app.route('/health', methods=["GET"])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'google-play-api'
    }), 200


@app.route('/search/<app_id>', methods=["GET"])
def search(app_id):
    """Search for apps with robust market/device iteration"""
    try:
        # Sanitize input
        app_id = re.sub(r'[^\w.]', '', app_id)
        logger.info(f"Search request for: {app_id}")

        # Get query parameters
        args = request.args
        details = args.get('details') is not None

        # Use the robust service
        result = gplay_service.search_app(app_id, details=details)

        if result:
            return jsonify({
                'success': True,
                'app_id': app_id,
                'apps': result['apps'],
                'metadata': {
                    'market': result['market'],
                    'device': result['device'],
                    'attempts': result['attempts']
                }
            }), 200
        else:
            return jsonify({
                'success': False,
                'app_id': app_id,
                'error': 'App not found in any market/device combination',
                'apps': {}
            }), 404

    except Exception as e:
        logger.error(f"Search error for {app_id}: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'app_id': app_id,
            'error': str(e),
            'apps': {}
        }), 500


@app.route('/download/<app_id>', methods=["GET", "HEAD"])
def download(app_id):
    """Download apps with robust market/device iteration"""
    try:
        # Sanitize input
        app_id = re.sub('\.apk$', '', app_id)
        app_id = re.sub(r'[^\w.]', '', app_id)
        logger.info(f"Download request for: {app_id}")

        # Use the robust service
        download_url = gplay_service.download_app(app_id)

        if download_url:
            logger.info(f"Redirecting to download URL for {app_id}")
            return redirect(download_url)
        else:
            logger.error(f"Download URL not found for {app_id}")
            return jsonify({
                'success': False,
                'app_id': app_id,
                'error': 'Download URL not found in any market/device combination'
            }), 404

    except Exception as e:
        logger.error(f"Download error for {app_id}: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'app_id': app_id,
            'error': str(e)
        }), 500


@app.errorhandler(404)
def not_found(_error):
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404


@app.errorhandler(500)
def internal_error(_error):
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500


if __name__ == '__main__':
    logger.info(f"Starting Google Play API server on {FLASK_HOST}:{FLASK_PORT}")
    app.run(host=FLASK_HOST, port=FLASK_PORT, debug=FLASK_DEBUG)

