import os
import re
import traceback
from flask import Flask, request, redirect
from gpapi.googleplay import GooglePlayAPI

app = Flask(__name__)


@app.route('/', methods=["GET"])
def root():
    return redirect('https://blog.drehsec.tk/my-google-play-api/')

@app.route('/search/<id>', methods=["GET"])
def search(id):
    try:
        id = re.sub(r'[^\w.]', '', id)
        print(id)
        args = request.args
        details = args.get('details')
        server = GooglePlayAPI('de_DE', 'Europe/Berlin')
        server.login(os.environ['email'], os.environ['password'])
        result = server.search(id)
        print(type(result))

        apps = {}
        i = 0
        for doc in result:
            if 'docid' in doc:
                print("doc: {}".format(doc["docid"]))
            for cluster in doc["child"]:
                print("\tcluster: {}".format(cluster["docid"]))
                #apps["cluster"] = cluster["docid"]
                for app in cluster["child"]:
                    print("\t\tapp: {}".format(app["docid"]))
                    if details is not None:
                        apps[app["docid"]] = app["details"]
                    else:
                        apps[app["docid"]] = app["details"]["appDetails"]["numDownloads"]

        return apps
    except Exception as e:
        print(e)
        print(traceback.format_exc())

@app.route('/download/<id>', methods=["GET"])
def download(id):
    try:
        id = re.sub('\.apk$', '', id)
        id = re.sub(r'[^\w.]', '', id)
        print(id)
        server = GooglePlayAPI('de_DE', 'Europe/Berlin')
        server.login(os.environ['email'], os.environ['password'])
        download = server.download(id)
        print(download)
        return redirect(download['downloadUrl'])
    except Exception as e:
        print(e)
        print(traceback.format_exc())
                                        
