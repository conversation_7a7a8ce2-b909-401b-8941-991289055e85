"""
Configuration module for Google Play API
"""
import os

# Market configurations with locale and timezone
MARKETS = [
    {'locale': 'en_US', 'timezone': 'America/New_York', 'name': 'US'},
    {'locale': 'en_GB', 'timezone': 'Europe/London', 'name': 'UK'},
    {'locale': 'de_DE', 'timezone': 'Europe/Berlin', 'name': 'Germany'},
    {'locale': 'fr_FR', 'timezone': 'Europe/Paris', 'name': 'France'},
    {'locale': 'es_ES', 'timezone': 'Europe/Madrid', 'name': 'Spain'},
    {'locale': 'it_IT', 'timezone': 'Europe/Rome', 'name': 'Italy'},
    {'locale': 'ja_<PERSON>', 'timezone': 'Asia/Tokyo', 'name': 'Japan'},
    {'locale': 'ko_KR', 'timezone': 'Asia/Seoul', 'name': 'South Korea'},
    {'locale': 'zh_CN', 'timezone': 'Asia/Shanghai', 'name': 'China'},
    {'locale': 'pt_BR', 'timezone': 'America/Sao_Paulo', 'name': 'Brazil'},
    {'locale': 'ru_RU', 'timezone': 'Europe/Moscow', 'name': 'Russia'},
    {'locale': 'en_AU', 'timezone': 'Australia/Sydney', 'name': 'Australia'},
    {'locale': 'en_CA', 'timezone': 'America/Toronto', 'name': 'Canada'},
    {'locale': 'en_IN', 'timezone': 'Asia/Kolkata', 'name': 'India'},
    {'locale': 'nl_NL', 'timezone': 'Europe/Amsterdam', 'name': 'Netherlands'},
    {'locale': 'sv_SE', 'timezone': 'Europe/Stockholm', 'name': 'Sweden'},
    {'locale': 'no_NO', 'timezone': 'Europe/Oslo', 'name': 'Norway'},
    {'locale': 'da_DK', 'timezone': 'Europe/Copenhagen', 'name': 'Denmark'},
    {'locale': 'fi_FI', 'timezone': 'Europe/Helsinki', 'name': 'Finland'},
    {'locale': 'pl_PL', 'timezone': 'Europe/Warsaw', 'name': 'Poland'},
]

# Device codenames from device_names.txt (cleaned up)
DEVICES = [
    'alien_jolla_bionic',
    'angler',
    'aries',
    'bacon',
    'bbb100',
    'BRAVIA_ATV2',
    'bravo',
    'bullhead',
    'cloudbook',
    'crackling',
    'cwv88s',
    'eeepad',
    'foster',
    'fp2',
    'fresh',
    'fs454',
    'gemini',
    'gtp7510',
    'gts3llte',
    'hammerhead',
    'hero2lte',
    'honami',
    'JP-1601',
    'K013_1',
    'kenzo',
    'm201',
    'm3xx',
    'maguro',
    'mako',
    'manta',
    'nxtl09',
    'oneplus3',
    'pico',
    'sailfish',
    'shamu',
    'sloane',
    't00q',
    'Tab_8_4C',
    'walleye',
    'wetekplay2',
    'walleyebacon',
    'walleyebacon9',
    'walleyebacon10',
    'jasmine_sprout',
]

# Priority devices (more likely to work)
PRIORITY_DEVICES = [
    'hero2lte',
    'angler',
    'bullhead',
    'hammerhead',
    'sailfish',
    'walleye',
    'bacon',
    'oneplus3',
    'mako',
    'shamu',
]

# Environment variables
GOOGLE_EMAIL = os.environ.get('email')
GOOGLE_PASSWORD = os.environ.get('password')

# API Configuration
MAX_RETRY_ATTEMPTS = 5  # Max combinations to try per request
CACHE_DURATION = 3600  # Cache successful configurations for 1 hour
LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')

# Flask configuration
FLASK_HOST = os.environ.get('FLASK_HOST', '0.0.0.0')
FLASK_PORT = int(os.environ.get('FLASK_PORT', 5000))
FLASK_DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
