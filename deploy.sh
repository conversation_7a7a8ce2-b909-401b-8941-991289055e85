#!/bin/bash

# Google Play API Deployment Script
set -e

echo "🚀 Google Play API Production Deployment"
echo "========================================"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your Google credentials:"
    echo "   - GOOGLE_EMAIL=<EMAIL>"
    echo "   - GOOGLE_PASSWORD=your-password"
    echo ""
    read -p "Press Enter after you've updated the .env file..."
fi

# Validate .env file has required variables
if ! grep -q "GOOGLE_EMAIL=" .env || ! grep -q "GOOGLE_PASSWORD=" .env; then
    echo "❌ .env file is missing required variables. Please check GOOGLE_EMAIL and GOOGLE_PASSWORD."
    exit 1
fi

# Check if credentials are still default values
if grep -q "<EMAIL>" .env || grep -q "your-google-password" .env; then
    echo "❌ Please update .env file with your actual Google credentials."
    exit 1
fi

echo "✅ Environment configuration validated"

# Build and start the service
echo "🔨 Building Docker image..."
docker-compose build

echo "🚀 Starting Google Play API service..."
docker-compose up -d

# Wait for service to be ready
echo "⏳ Waiting for service to be ready..."
sleep 10

# Test health endpoint
echo "🏥 Testing health endpoint..."
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ Service is healthy and ready!"
else
    echo "❌ Service health check failed. Checking logs..."
    docker-compose logs google-play-api
    exit 1
fi

echo ""
echo "🎉 Deployment successful!"
echo ""
echo "📋 Service Information:"
echo "   - Health: http://localhost:5000/health"
echo "   - Search: http://localhost:5000/search/{app_id}"
echo "   - Download: http://localhost:5000/download/{app_id}"
echo ""
echo "📊 Management Commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Stop service: docker-compose down"
echo "   - Restart: docker-compose restart"
echo ""
echo "🧪 Test the API:"
echo "   curl http://localhost:5000/health"
echo "   curl http://localhost:5000/search/com.firsty.app"
echo ""
