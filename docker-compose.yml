version: '3.8'

services:
  google-play-api:
    build: .
    container_name: google-play-api
    ports:
      - "5000:5000"
    environment:
      # Google Play credentials (set these in .env file or environment)
      - email=${GOOGLE_EMAIL}
      - password=${GOOGLE_PASSWORD}
      
      # Flask configuration
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5000
      - FLASK_DEBUG=false
      
      # Logging
      - LOG_LEVEL=INFO
      
    # Environment file for sensitive data
    env_file:
      - .env
    
    # Restart policy
    restart: unless-stopped
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
