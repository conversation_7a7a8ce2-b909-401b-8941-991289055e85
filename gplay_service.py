"""
Google Play API service with robust market/device iteration
"""
import logging
import time
import random
from typing import Dict, List, Optional, Tuple
from gpapi.googleplay import GooglePlayAPI, RequestError
from config import MARKETS, DEVICES, PRIORITY_DEVICES, GOOGLE_EMAIL, GOO<PERSON>LE_PASSWORD, MAX_RETRY_ATTEMPTS

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GooglePlayService:
    def __init__(self):
        self.successful_configs = {}  # Cache for successful market/device combinations
        self.last_successful_time = {}

    def _get_market_device_combinations(self) -> List[Tuple[Dict, str]]:
        """
        Generate market/device combinations, prioritizing likely-to-work combinations
        """
        combinations = []

        # First, try priority devices with major markets
        major_markets = [m for m in MARKETS if m['locale'] in ['en_US', 'en_GB', 'de_DE', 'fr_FR']]
        for market in major_markets:
            for device in PRIORITY_DEVICES:
                combinations.append((market, device))

        # Then try all markets with priority devices
        for market in MARKETS:
            for device in PRIORITY_DEVICES:
                if (market, device) not in combinations:
                    combinations.append((market, device))

        # Finally, add remaining combinations
        for market in MARKETS:
            for device in DEVICES:
                if (market, device) not in combinations:
                    combinations.append((market, device))

        # Shuffle to avoid always hitting the same combinations first
        random.shuffle(combinations)
        return combinations

    def _create_api_client(self, market: Dict, device: str) -> GooglePlayAPI:
        """Create and login to Google Play API client"""
        try:
            api = GooglePlayAPI(
                locale=market['locale'],
                timezone=market['timezone'],
                device_codename=device
            )
            api.login(GOOGLE_EMAIL, GOOGLE_PASSWORD)
            return api
        except Exception as e:
            logger.debug(f"Failed to create API client for {market['name']}/{device}: {e}")
            raise

    def _is_config_cached(self, app_id: str, market: Dict, device: str) -> bool:
        """Check if this configuration was recently successful for this app"""
        cache_key = f"{app_id}_{market['locale']}_{device}"
        if cache_key in self.successful_configs:
            # Check if cache is still valid (1 hour)
            if time.time() - self.last_successful_time[cache_key] < 3600:
                return True
            else:
                # Remove expired cache
                del self.successful_configs[cache_key]
                del self.last_successful_time[cache_key]
        return False

    def _cache_successful_config(self, app_id: str, market: Dict, device: str):
        """Cache a successful configuration"""
        cache_key = f"{app_id}_{market['locale']}_{device}"
        self.successful_configs[cache_key] = (market, device)
        self.last_successful_time[cache_key] = time.time()

    def search_app(self, app_id: str, details: bool = False) -> Optional[Dict]:
        """
        Search for an app across different markets and devices until found
        """
        logger.info(f"Searching for app: {app_id}")

        combinations = self._get_market_device_combinations()
        attempts = 0

        for market, device in combinations:
            if attempts >= MAX_RETRY_ATTEMPTS:
                logger.warning(f"Max retry attempts ({MAX_RETRY_ATTEMPTS}) reached for {app_id}")
                break

            attempts += 1

            try:
                logger.debug(f"Trying {market['name']}/{device} for {app_id}")
                api = self._create_api_client(market, device)
                result = api.search(app_id)

                if result:
                    apps = self._parse_search_results(result, details)
                    if apps:
                        logger.info(f"Found {app_id} using {market['name']}/{device}")
                        self._cache_successful_config(app_id, market, device)
                        return {
                            'apps': apps,
                            'market': market['name'],
                            'device': device,
                            'attempts': attempts
                        }

            except RequestError as e:
                logger.debug(f"Request error for {market['name']}/{device}: {e}")
                continue
            except Exception as e:
                logger.debug(f"Unexpected error for {market['name']}/{device}: {e}")
                continue

        logger.error(f"App {app_id} not found in any market/device combination")
        return None

    def download_app(self, app_id: str) -> Optional[str]:
        """
        Download an app across different markets and devices until successful
        """
        logger.info(f"Downloading app: {app_id}")

        combinations = self._get_market_device_combinations()
        attempts = 0

        for market, device in combinations:
            if attempts >= MAX_RETRY_ATTEMPTS:
                logger.warning(f"Max retry attempts ({MAX_RETRY_ATTEMPTS}) reached for {app_id}")
                break

            attempts += 1

            try:
                logger.debug(f"Trying download {market['name']}/{device} for {app_id}")
                api = self._create_api_client(market, device)
                download_result = api.download(app_id)

                if download_result and 'downloadUrl' in download_result:
                    logger.info(f"Successfully got download URL for {app_id} using {market['name']}/{device}")
                    self._cache_successful_config(app_id, market, device)
                    return download_result['downloadUrl']

            except RequestError as e:
                error_msg = str(e)
                logger.debug(f"Request error for {market['name']}/{device}: {error_msg}")

                # Skip this combination if it's a known error that won't work
                if 'DF-DFERH-01' in error_msg or 'not available' in error_msg.lower():
                    continue

            except Exception as e:
                logger.debug(f"Unexpected error for {market['name']}/{device}: {e}")
                continue

        logger.error(f"Download URL for {app_id} not found in any market/device combination")
        return None

    def _parse_search_results(self, result: List, details: bool = False) -> Dict:
        """Parse search results from Google Play API"""
        apps = {}

        try:
            for doc in result:
                if 'docid' in doc:
                    logger.debug(f"doc: {doc['docid']}")

                for cluster in doc.get("child", []):
                    logger.debug(f"\tcluster: {cluster.get('docid', 'unknown')}")

                    for app in cluster.get("child", []):
                        app_id = app.get("docid")
                        if app_id:
                            logger.debug(f"\t\tapp: {app_id}")
                            if details:
                                apps[app_id] = app.get("details", {})
                            else:
                                num_downloads = app.get("details", {}).get("appDetails", {}).get("numDownloads", "Unknown")
                                apps[app_id] = num_downloads

        except Exception as e:
            logger.error(f"Error parsing search results: {e}")

        return apps
