#!/usr/bin/env python3
"""
Test script to validate the code structure without external dependencies
"""

def test_config():
    """Test configuration loading"""
    try:
        import config
        print("✓ Config module loaded successfully")
        print(f"  - Found {len(config.MARKETS)} markets")
        print(f"  - Found {len(config.DEVICES)} devices")
        print(f"  - Found {len(config.PRIORITY_DEVICES)} priority devices")
        return True
    except Exception as e:
        print(f"✗ Config module failed: {e}")
        return False

def test_api_structure():
    """Test API module structure"""
    try:
        # Test if we can import the basic structure
        import re
        import logging
        from flask import Flask, request, redirect, jsonify
        print("✓ API dependencies available")
        
        # Test regex patterns used in the API
        test_id = "com.example.app.apk"
        cleaned = re.sub('\.apk$', '', test_id)
        cleaned = re.sub(r'[^\w.]', '', cleaned)
        assert cleaned == "com.example.app"
        print("✓ Input sanitization logic works")
        
        return True
    except Exception as e:
        print(f"✗ API structure test failed: {e}")
        return False

def test_market_device_combinations():
    """Test market/device combination logic"""
    try:
        import config
        
        # Simulate the combination logic from gplay_service
        combinations = []
        
        # Major markets with priority devices
        major_markets = [m for m in config.MARKETS if m['locale'] in ['en_US', 'en_GB', 'de_DE', 'fr_FR']]
        for market in major_markets:
            for device in config.PRIORITY_DEVICES:
                combinations.append((market, device))
        
        print(f"✓ Generated {len(combinations)} priority combinations")
        print(f"  - Example: {combinations[0][0]['name']}/{combinations[0][1]}")
        
        # Test that we have good coverage
        assert len(combinations) >= 40  # 4 major markets * 10 priority devices
        print("✓ Sufficient market/device combinations available")
        
        return True
    except Exception as e:
        print(f"✗ Market/device combination test failed: {e}")
        return False

def test_docker_files():
    """Test Docker configuration files"""
    try:
        import os
        
        # Check Dockerfile exists and has key components
        with open('Dockerfile', 'r') as f:
            dockerfile_content = f.read()
            assert 'debian:11' in dockerfile_content
            assert 'python3' in dockerfile_content
            assert 'requirements.txt' in dockerfile_content
            print("✓ Dockerfile structure is correct")
        
        # Check docker-compose.yml
        with open('docker-compose.yml', 'r') as f:
            compose_content = f.read()
            assert 'google-play-api' in compose_content
            assert '5000:5000' in compose_content
            assert 'healthcheck' in compose_content
            print("✓ Docker Compose configuration is correct")
        
        # Check .env.example
        with open('.env.example', 'r') as f:
            env_content = f.read()
            assert 'GOOGLE_EMAIL' in env_content
            assert 'GOOGLE_PASSWORD' in env_content
            print("✓ Environment template is correct")
        
        return True
    except Exception as e:
        print(f"✗ Docker files test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Google Play API Production Setup")
    print("=" * 50)
    
    tests = [
        test_config,
        test_api_structure,
        test_market_device_combinations,
        test_docker_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The production setup is ready.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and add your Google credentials")
        print("2. Run: docker-compose up -d")
        print("3. Test: curl http://localhost:5000/health")
    else:
        print("❌ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
